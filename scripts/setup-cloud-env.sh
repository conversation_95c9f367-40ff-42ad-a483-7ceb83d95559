#!/bin/bash

# Setup script for Google Cloud environment variables
# This script helps you set up the required environment variables for Cloud Run deployment

set -e

PROJECT_ID=${1:-"ora-phase1"}
REGION=${2:-"us-central1"}

echo "🔧 Setting up environment variables for ORA Hume deployment"
echo "📋 Project ID: $PROJECT_ID"
echo "🌍 Region: $REGION"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    echo "❌ You are not authenticated with gcloud. Please run 'gcloud auth login' first."
    exit 1
fi

echo "✅ gcloud CLI is ready"
echo ""

# Function to set environment variable
set_env_var() {
    local var_name=$1
    local var_description=$2
    local var_example=$3
    local is_secret=${4:-false}
    
    echo "🔑 Setting up $var_name"
    echo "   Description: $var_description"
    if [ "$is_secret" = false ]; then
        echo "   Example: $var_example"
    fi
    
    read -p "   Enter value: " var_value
    
    if [ -z "$var_value" ]; then
        echo "   ⚠️  Skipping $var_name (empty value)"
        return
    fi
    
    # Store in Google Secret Manager for sensitive data
    if [ "$is_secret" = true ]; then
        echo "   📦 Storing in Secret Manager..."
        echo -n "$var_value" | gcloud secrets create "$var_name" --data-file=- --project="$PROJECT_ID" 2>/dev/null || \
        echo -n "$var_value" | gcloud secrets versions add "$var_name" --data-file=- --project="$PROJECT_ID"
        echo "   ✅ $var_name stored in Secret Manager"
    else
        # For non-sensitive data, we'll just echo the command to run
        echo "   💾 Add this to your deployment: $var_name=$var_value"
    fi
    
    echo ""
}

echo "📝 Please provide the following environment variables:"
echo "   (Press Enter to skip any variable)"
echo ""

# Database Configuration
set_env_var "DATABASE_URL" "PostgreSQL connection string" "DATABASE_URL=postgresql://ora_user:@**************:5432/ora_hume_db" true

# Google OAuth Configuration  
set_env_var "GOOGLE_CLIENT_ID" "Google OAuth Client ID" "123456789-abcdef.apps.googleusercontent.com" true
set_env_var "GOOGLE_CLIENT_SECRET" "Google OAuth Client Secret" "GOCSPX-abcdef123456" true

# JWT Configuration
set_env_var "JWT_SECRET" "JWT signing secret (generate a random string)" "your-super-secret-jwt-key" true
set_env_var "SESSION_SECRET" "Session signing secret (generate a random string)" "your-session-secret" true

# Hume API Configuration
set_env_var "HUME_API_KEY" "Hume API Key" "your-hume-api-key" true
set_env_var "HUME_SECRET_KEY" "Hume Secret Key" "your-hume-secret-key" true
set_env_var "HUME_CONFIG_ID" "Hume Configuration ID" "your-hume-config-id" true

echo "🎉 Environment variable setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Make sure you have a PostgreSQL database set up (Cloud SQL recommended)"
echo "2. Update your cloudbuild.yaml to reference the secrets"
echo "3. Run your deployment: gcloud builds submit --config=cloudbuild.yaml"
echo ""
echo "💡 To use secrets in Cloud Run, update your cloudbuild.yaml to use:"
echo "   --set-secrets=DATABASE_URL=DATABASE_URL:latest"
echo "   instead of --set-env-vars for sensitive data"
